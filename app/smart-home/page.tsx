import "./SmartHome.scss";
import { Box, Typography } from "@mui/material";
import Image from "next/image";
import { HeatingImage, SmartHomeImage } from "@/public/index";

const HeatingVentilationPage = () => {
  return (
    <Box className="smart-home-container">
      <Box className="smart-home-page-content">
        <Box
          className="smart-home-header-image"
          data-aos="fade-up"
          data-aos-duration="1000"
        >
          <Image src={SmartHomeImage} alt="smart&homes" />
          <Box className="banner-text-overlay">
            <Typography className="banner-title">
              Smart Home & Buildings
            </Typography>
          </Box>
        </Box>
      </Box>

      <Box className="smart-home-subtitle">
        <Typography className="subtitle-one">
          Aadvik Teklabs, a leading engineering service provider in the smart
          home domain, delivers fully customized home automation solutions,
          demonstrating a strong commitment to end-to-end automation and
          security. By driving the development of futuristic living spaces,
          below are key expertise which Aadvik Team has evolved to build a
          proven smart home system -
        </Typography>
      </Box>
    </Box>
  );
};

export default HeatingVentilationPage;
