import "./ai_visiontek.scss";
import { Box, List, ListItem, Typography } from "@mui/material";
import Image from "next/image";
import {
  web_technology_img9,
  web_technology_img10,
  web_technology_img11,
  web_technology_img12,
  ai_visiontek1,
} from "@/public/index";
import { aiVisionTekOfferings, AIVisionTekContent } from "@/constant/index";
import OfferingPairs from "@/components/OfferingPairs/OfferingPairs";

const AiVisionTekPage = () => {
  return (
    <Box className="ai-vision-tek-page-container">
      <Box className="ai-vision-tek-page-content">
        <Box
          className="ai-vision-tek-header-image"
          data-aos="fade-up"
          data-aos-duration="500"
        >
          <Image src={ai_visiontek1} alt="AI VisionTek" />
        </Box>

        <Box className="ai-vision-tek-content">
          <Typography
            variant="h2"
            className="ai-vision-tek-title"
            data-aos="fade-up"
            data-aos-duration="500"
          >
            {AIVisionTekContent.title}
          </Typography>
          <Box
            className="ai-vision-tek-description-container"
            data-aos="fade-up"
            data-aos-duration="500"
          >
            {AIVisionTekContent.mainDescription.map((text, index) => (
              <Typography
                key={index}
                variant="body1"
                className="ai-vision-tek-description"
              >
                {text}
              </Typography>
            ))}
          </Box>

          <Box className="ai-ml" data-aos="fade-up" data-aos-duration="500">
            <Typography variant="h2" className="ai-ml-title">
              {AIVisionTekContent.aiMlSection.title}
            </Typography>
            {AIVisionTekContent.aiMlSection.description.map((text, index) => (
              <Typography key={index} className="ai-ml-description">
                {text}
              </Typography>
            ))}

            <Box
              className="ai-ml-tech-stack"
              data-aos="fade-up"
              data-aos-duration="500"
            >
              <List>
                {AIVisionTekContent.aiMlSection.techStack
                  .slice(0, 2)
                  .map((item, index) => (
                    <ListItem key={index}>❖ {item}</ListItem>
                  ))}
              </List>
              <List>
                {AIVisionTekContent.aiMlSection.techStack
                  .slice(2)
                  .map((item, index) => (
                    <ListItem key={index}>❖ {item}</ListItem>
                  ))}
              </List>
            </Box>
          </Box>

          <Box
            className="our-offerings"
            data-aos="fade-up"
            data-aos-duration="500"
          >
            <Typography variant="h2" className="our-offerings-title">
              What we Offer!
            </Typography>

            <OfferingPairs
              offerings={aiVisionTekOfferings}
              className="our-offerings-list"
            />
          </Box>

          <Box
            className="success-stories"
            data-aos="fade-up"
            data-aos-duration="500"
          >
            <Typography variant="h2" className="success-stories-description">
              {AIVisionTekContent.successStories.title}
            </Typography>

            <Box className="bullets-point">
              {AIVisionTekContent.successStories.stories.map((story, index) => (
                <ListItem key={index}>{story}</ListItem>
              ))}
            </Box>
          </Box>

          <Box
            className="case-study"
            data-aos="fade-up"
            data-aos-duration="500"
          >
            <Typography variant="h2" className="case-study-title">
              {AIVisionTekContent.caseStudies.title}
            </Typography>
            <Box className="case-study-images">
              {AIVisionTekContent.caseStudies.studies.map((study, index) => {
                const images = [
                  web_technology_img9,
                  web_technology_img10,
                  web_technology_img11,
                  web_technology_img12,
                ];
                return (
                  <Box
                    key={index}
                    className="case-study-image-item"
                    data-aos="fade-up"
                    data-aos-duration="500"
                    data-aos-delay={index * 100}
                  >
                    <Image
                      src={images[index]}
                      alt="Web Technologies"
                      className="case-study-image"
                    />
                    <Typography className="image-title">
                      {study.title}
                    </Typography>
                  </Box>
                );
              })}
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default AiVisionTekPage;
