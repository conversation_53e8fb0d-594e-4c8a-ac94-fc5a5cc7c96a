"use client";

import React from "react";
import { Box, Typography, useTheme, useMediaQuery } from "@mui/material";
import "./Platforms.scss";
import FeatureCard from "@/components/FeatureCard/FeatureCard";
import ManagementModules from "@/components/ManagementModules/ManagementModules";

const featureBlocksTop = [
  "Integrated HRMS",
  "Integrated CRM",
  "Finance Payroll",
];
const featureBlocksBottom = [
  "Asset Management",
  "Client Engagement",
  "Operation Management",
];

const Platform = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  return (
    <Box className="platforms-container">
      <Box className="platforms-container-row">
        {featureBlocksTop.map((item, index) => {
          const [line1, line2] = item.split(/(?<=\w)\s/);
          return (
            <FeatureCard key={index} titleLine1={line1} titleLine2={line2} />
          );
        })}
      </Box>

      <Box className="platforms-container-title-row">
        <Typography className="platforms-container-title">
          Integrated Web Platform for Business Excellence
        </Typography>
      </Box>

      <Box className="platforms-container-row">
        {featureBlocksBottom.map((item, index) => {
          const [line1, line2] = item.split(/(?<=\w)\s/);
          return (
            <FeatureCard key={index} titleLine1={line1} titleLine2={line2} />
          );
        })}
      </Box>

      <Box className="sync-master">
        <Typography className="sync-master-header">
          SyncMaster - Your Seamless Growth Engine
        </Typography>
        <Typography className="sync-master-description-one">
          SyncMaster is responsible to handle numerous of our day to day
          operation tasks start from simple HRM to Project Management and
          integrated CRM web tool which can provide single window platform for
          all that an organization needs. Start from day to day Task assignment
          and employee attendance, to the real time project status snapshot,
          management can easily get the insight about the health of an
          organization , its assets , employee head counts , departments and
          current ongoing project at different client places.
        </Typography>
        <Typography className="sync-master-description-two">
          The platform empowers business leaders to automate routine tasks,
          monitor key performance metrics, and support strategic
          decision-making—driving efficiency, productivity, and growth across
          the organization.
        </Typography>
      </Box>

      <ManagementModules />
    </Box>
  );
};

export default Platform;
