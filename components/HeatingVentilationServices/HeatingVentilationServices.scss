@use "../../styles/variables" as *;

.heating-ventilation-services-container {
  width: 100%;

  .heating-ventilation-services-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 40px;
    max-width: 2100px;
    margin: 0 auto;
    // padding: 0 20px;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 20px;
      padding: 0 15px;
    }
  }
}

.heating-ventilation-service-card {
  background-color: #093246b2;
  border: 1px solid #2499e2;
  border-radius: 8px;
  padding: 30px 25px;
  min-height: 300px;
  transition: all 0.3s ease;

  .heating-ventilation-service-title {
    font-family: Poppins;
    font-weight: 300;
    font-size: 24px;
    line-height: 30px;
    letter-spacing: 0%;
    text-align: center;

    margin-bottom: 25px;
    padding-bottom: 10px;

    @media (max-width: 768px) {
      font-size: 18px;
      line-height: 26px;
      margin-bottom: 20px;
    }
  }

  .heating-ventilation-service-list {
    padding: 0;
    margin: 0;

    .heating-ventilation-service-item {
      font-family: Poppins;
      font-weight: 400;
      font-size: 16px;
      line-height: 28px;
      letter-spacing: 0%;
      color: $text-white;
      display: flex;
      align-items: flex-start;
      padding: 8px 0;
      margin: 0;

      &::before {
        content: "•";
        color: #2499e2;
        font-weight: bold;
        margin-right: 12px;
        margin-top: 2px;
        flex-shrink: 0;
      }

      @media (max-width: 768px) {
        font-size: 15px;
        line-height: 26px;
        padding: 6px 0;
      }

      @media (max-width: 480px) {
        font-size: 14px;
        line-height: 24px;
      }
    }
  }

  @media (max-width: 768px) {
    padding: 25px 20px;
    min-height: 250px;
  }

  @media (max-width: 480px) {
    padding: 20px 15px;
    min-height: 200px;
  }
}
