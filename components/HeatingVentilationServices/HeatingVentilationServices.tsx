import "./HeatingVentilationServices.scss";
import { Box, List, ListItem, Typography } from "@mui/material";

interface HeatingVentilationServiceProps {
  title: string;
  bulletPoints: string[];
}

const HeatingVentilationServiceCard = ({
  title,
  bulletPoints,
}: HeatingVentilationServiceProps) => {
  return (
    <Box className="heating-ventilation-service-card">
      <Typography
        className="heating-ventilation-service-title"
        data-aos="fade-up"
        data-aos-duration="1000"
      >
        {title}
      </Typography>
      <List className="heating-ventilation-service-list" data-aos="fade-up" data-aos-duration="1000">
        {bulletPoints.map((point, index) => (
          <ListItem key={index} className="heating-ventilation-service-item">
            {point}
          </ListItem>
        ))}
      </List>
    </Box>
  );
};

interface HeatingVentilationServicesProps {
  services: HeatingVentilationServiceProps[];
}

const HeatingVentilationServices = ({ services }: HeatingVentilationServicesProps) => {
  return (
    <Box className="heating-ventilation-services-container">
      <Box className="heating-ventilation-services-grid">
        {services.map((service, index) => (
          <HeatingVentilationServiceCard
            key={index}
            title={service.title}
            bulletPoints={service.bulletPoints}
          />
        ))}
      </Box>
    </Box>
  );
};

export default HeatingVentilationServices;
