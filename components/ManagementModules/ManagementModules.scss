.management-modules {
  width: 100%;
  // padding: 2rem 0;
  
  .management-modules-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    
    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }
  }
  
  .management-module-card {
    background: linear-gradient(135deg, #1a2332 0%, #2a3441 100%);
    border: 1px solid #3a4a5c;
    border-radius: 12px;
    padding: 2rem;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
      border-color: #4a9eff;
    }
    
    .management-module-title {
      color: #ffffff;
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 1rem;
      text-align: center;
      
      @media (max-width: 768px) {
        font-size: 1.3rem;
      }
    }
    
    .management-module-description {
      color: #b8c5d1;
      font-size: 0.95rem;
      line-height: 1.6;
      text-align: justify;
      
      @media (max-width: 768px) {
        font-size: 0.9rem;
        text-align: left;
      }
    }
  }
}
