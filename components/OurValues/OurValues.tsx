import React from "react";
import "@/components/OurValues/OurValues.scss";
import { Box, Typography } from "@mui/material";
import Image from "next/image";
import { OurValues_Img } from "@/public/index";

const OurValues: React.FC = () => {
  return (
    <Box className="our-values-section-container">
      <Typography variant="h2" className="our-values-title">
        Our Values
      </Typography>
      <Image
        src={OurValues_Img}
        alt="Our Values Section Image"
        // fill
        style={{
          objectFit: "cover",
          borderRadius: "8px",
          // transition: "transform 0.3s ease",
        }}
      />
    </Box>
  );
};

export default OurValues;
